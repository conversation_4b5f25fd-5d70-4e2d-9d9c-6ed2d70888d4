#!/usr/bin/env node
const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const http = require('http');
const os = require('os');

// 配置
const CONFIG = {
  port: 3000,
  host: 'localhost',
  startupTimeout: 30000,
  checkInterval: 1000,
  maxRetries: 5
};

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查Node.js环境
function checkNodeEnvironment() {
  log('\n📋 检查Node.js环境...', 'blue');
  try {
    const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
    log(`✅ Node.js版本: ${nodeVersion}`, 'green');
    log(`✅ npm版本: ${npmVersion}`, 'green');
    return true;
  } catch (error) {
    log('❌ Node.js环境检查失败', 'red');
    log('请确保已安装Node.js和npm', 'red');
    return false;
  }
}

// 检查package.json完整性
function checkPackageJson() {
  log('\n📋 检查package.json...', 'blue');
  const packagePath = path.join(process.cwd(), 'package.json');
  
  if (!fs.existsSync(packagePath)) {
    log('❌ package.json文件不存在', 'red');
    return false;
  }

  try {
    const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    if (!packageData.scripts || !packageData.scripts.dev) {
      log('❌ package.json中缺少dev脚本', 'red');
      return false;
    }

    log('✅ package.json检查通过', 'green');
    return true;
  } catch (error) {
    log('❌ package.json格式错误', 'red');
    return false;
  }
}

// 检查并安装依赖
function checkAndInstallDependencies() {
  log('\n📦 检查依赖包...', 'blue');
  
  const nodeModulesPath = path.join(process.cwd(), 'node_modules');
  const packageLockPath = path.join(process.cwd(), 'package-lock.json');
  
  if (!fs.existsSync(nodeModulesPath) || !fs.existsSync(packageLockPath)) {
    log('🔄 检测到依赖缺失，开始安装...', 'yellow');
    
    try {
      execSync('npm install', { 
        stdio: 'inherit',
        cwd: process.cwd()
      });
      log('✅ 依赖安装完成', 'green');
    } catch (error) {
      log('❌ 依赖安装失败', 'red');
      log('错误信息: ' + error.message, 'red');
      return false;
    }
  } else {
    log('✅ 依赖已安装', 'green');
  }
  
  return true;
}

// 检查端口是否可用
function checkPort(port) {
  return new Promise((resolve) => {
    const server = http.createServer();
    server.listen(port, CONFIG.host, () => {
      server.close();
      resolve(true);
    });
    server.on('error', () => {
      resolve(false);
    });
  });
}

// 等待服务器启动
function waitForServer(port) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const checkServer = () => {
      const req = http.request({
        hostname: CONFIG.host,
        port: port,
        path: '/',
        method: 'GET',
        timeout: 2000
      }, (res) => {
        if (res.statusCode >= 200 && res.statusCode < 400) {
          resolve(true);
        } else {
          setTimeout(checkServer, CONFIG.checkInterval);
        }
      });
      
      req.on('error', () => {
        if (Date.now() - startTime > CONFIG.startupTimeout) {
          reject(new Error('服务器启动超时'));
        } else {
          setTimeout(checkServer, CONFIG.checkInterval);
        }
      });
      
      req.on('timeout', () => {
        req.destroy();
        setTimeout(checkServer, CONFIG.checkInterval);
      });
      
      req.end();
    };
    
    checkServer();
  });
}

// 打开浏览器
function open